<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业登录平台 - 安全访问</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-primary: #ffffff;
            --background-secondary: #f8fafc;
            --background-tertiary: #f1f5f9;
            --text-primary: #0f172a;
            --text-secondary: #475569;
            --text-tertiary: #94a3b8;
            --border-color: #e2e8f0;
            --border-focus: #3b82f6;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #2563eb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            line-height: 1.6;
            color: var(--text-primary);
            position: relative;
            overflow-x: hidden;
        }

        /* 背景装饰元素 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            pointer-events: none;
        }

        .login-container {
            background: var(--background-primary);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            width: 100%;
            max-width: 28rem;
            position: relative;
            overflow: hidden;
            animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(2rem) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .login-header {
            padding: 2.5rem 2.5rem 1.5rem;
            text-align: center;
            background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
            border-bottom: 1px solid var(--border-color);
        }

        .company-logo {
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: var(--shadow-md);
        }

        .company-logo svg {
            width: 2rem;
            height: 2rem;
            color: white;
        }

        .login-title {
            color: var(--text-primary);
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            letter-spacing: -0.025em;
        }

        .login-subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 400;
        }

        .login-form {
            padding: 2rem 2.5rem 2.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            color: var(--text-primary);
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            letter-spacing: 0.025em;
        }

        .form-input-wrapper {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 1rem;
            font-weight: 400;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            background: var(--background-primary);
            color: var(--text-primary);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--border-focus);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: var(--background-primary);
        }

        .form-input::placeholder {
            color: var(--text-tertiary);
            font-weight: 400;
        }

        .form-input.error {
            border-color: var(--error-color);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 0.875rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-tertiary);
            width: 1.25rem;
            height: 1.25rem;
            pointer-events: none;
        }

        .form-input.with-icon {
            padding-left: 2.75rem;
        }

        .password-toggle {
            position: absolute;
            right: 0.875rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-tertiary);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: var(--radius-sm);
            transition: all 0.2s ease;
            width: 1.5rem;
            height: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .password-toggle:hover {
            color: var(--text-secondary);
            background: var(--background-tertiary);
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            font-size: 0.875rem;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            cursor: pointer;
            user-select: none;
        }

        .remember-me input[type="checkbox"] {
            width: 1rem;
            height: 1rem;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .forgot-password {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .forgot-password:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        .error-message {
            color: var(--error-color);
            font-size: 0.75rem;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .login-button {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            border: none;
            padding: 0.875rem 1.5rem;
            border-radius: var(--radius-md);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            letter-spacing: 0.025em;
        }

        .login-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }

        .login-button:active {
            transform: translateY(0);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .button-spinner {
            width: 1.25rem;
            height: 1.25rem;
        }

        .spinner {
            width: 100%;
            height: 100%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
            color: var(--text-tertiary);
            font-size: 0.875rem;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--border-color);
            z-index: 1;
        }

        .divider span {
            background: var(--background-primary);
            padding: 0 1rem;
            position: relative;
            z-index: 2;
        }

        .enterprise-login {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .enterprise-button {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--background-primary);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .enterprise-button:hover {
            border-color: var(--primary-color);
            background: var(--background-secondary);
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        .enterprise-icon {
            width: 1.25rem;
            height: 1.25rem;
        }

        .security-notice {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem;
            background: var(--background-secondary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            margin-bottom: 1.5rem;
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .security-icon {
            width: 1rem;
            height: 1rem;
            color: var(--success-color);
        }

        .login-footer {
            padding: 1.5rem 2.5rem 2rem;
            border-top: 1px solid var(--border-color);
            background: var(--background-secondary);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.75rem;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .footer-links a:hover {
            color: var(--primary-color);
            text-decoration: underline;
        }

        .footer-text {
            text-align: center;
        }

        .footer-text p {
            color: var(--text-tertiary);
            font-size: 0.75rem;
        }

        /* 响应式设计 */
        @media (max-width: 640px) {
            body {
                padding: 0.5rem;
            }

            .login-container {
                max-width: 100%;
            }

            .login-header {
                padding: 2rem 1.5rem 1rem;
            }

            .login-form {
                padding: 1.5rem;
            }

            .login-footer {
                padding: 1rem 1.5rem;
            }

            .footer-links {
                gap: 1rem;
            }

            .login-title {
                font-size: 1.5rem;
            }
        }

        /* 深色模式支持 */
        @media (prefers-color-scheme: dark) {
            :root {
                --background-primary: #1e293b;
                --background-secondary: #334155;
                --background-tertiary: #475569;
                --text-primary: #f1f5f9;
                --text-secondary: #cbd5e1;
                --text-tertiary: #94a3b8;
                --border-color: #475569;
            }
        }

        /* 高对比度模式 */
        @media (prefers-contrast: high) {
            :root {
                --border-color: #000000;
                --text-tertiary: #000000;
            }
        }

        /* 减少动画 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="company-logo">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                </svg>
            </div>
            <h1 class="login-title">企业平台</h1>
            <p class="login-subtitle">安全登录到您的工作空间</p>
        </div>

        <form class="login-form" onsubmit="handleLogin(event)">
            <div class="form-group">
                <label class="form-label" for="email">企业邮箱</label>
                <div class="form-input-wrapper">
                    <svg class="input-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                    </svg>
                    <input
                        type="email"
                        id="email"
                        class="form-input with-icon"
                        placeholder="<EMAIL>"
                        required
                        autocomplete="email"
                        aria-describedby="email-error"
                    >
                </div>
                <div id="email-error" class="error-message" style="display: none;"></div>
            </div>

            <div class="form-group">
                <label class="form-label" for="password">密码</label>
                <div class="form-input-wrapper">
                    <svg class="input-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18,8h-1V6c0-2.76-2.24-5-5-5S7,3.24,7,6v2H6c-1.1,0-2,0.9-2,2v10c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V10C20,8.9,19.1,8,18,8z M12,17c-1.1,0-2-0.9-2-2s0.9-2,2-2s2,0.9,2,2S13.1,17,12,17z M15.1,8H8.9V6c0-1.71,1.39-3.1,3.1-3.1s3.1,1.39,3.1,3.1V8z"/>
                    </svg>
                    <input
                        type="password"
                        id="password"
                        class="form-input with-icon"
                        placeholder="输入您的密码"
                        required
                        autocomplete="current-password"
                        aria-describedby="password-error"
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword()" aria-label="显示密码">
                        <svg id="eye-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                        </svg>
                    </button>
                </div>
                <div id="password-error" class="error-message" style="display: none;"></div>
            </div>

            <div class="form-options">
                <label class="remember-me">
                    <input type="checkbox" id="remember" name="remember">
                    <span>保持登录状态</span>
                </label>
                <a href="#" class="forgot-password" onclick="handleForgotPassword(event)">忘记密码？</a>
            </div>

            <button type="submit" class="login-button" id="loginBtn">
                <span class="button-text">安全登录</span>
                <div class="button-spinner" style="display: none;">
                    <svg class="spinner" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                            <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                            <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                        </circle>
                    </svg>
                </div>
            </button>

            <div class="divider">
                <span>或使用企业账户登录</span>
            </div>

            <div class="enterprise-login">
                <button type="button" class="enterprise-button" onclick="handleSSOLogin('microsoft')">
                    <svg class="enterprise-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zM24 11.4H12.6V0H24v11.4z"/>
                    </svg>
                    <span>Microsoft 365</span>
                </button>

                <button type="button" class="enterprise-button" onclick="handleSSOLogin('google')">
                    <svg class="enterprise-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    <span>Google Workspace</span>
                </button>
            </div>

            <div class="security-notice">
                <svg class="security-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/>
                </svg>
                <span>您的连接已通过 SSL 加密保护</span>
            </div>
        </form>

        <div class="login-footer">
            <div class="footer-links">
                <a href="#" onclick="handleSupport(event)">技术支持</a>
                <a href="#" onclick="handlePrivacy(event)">隐私政策</a>
                <a href="#" onclick="handleTerms(event)">服务条款</a>
            </div>
            <div class="footer-text">
                <p>&copy; 2025 企业平台. 保留所有权利.</p>
            </div>
        </div>
    </div>

    <script>
        // 表单验证和提交
        function handleLogin(event) {
            event.preventDefault();

            const email = document.getElementById('email');
            const password = document.getElementById('password');
            const loginBtn = document.getElementById('loginBtn');
            const buttonText = loginBtn.querySelector('.button-text');
            const buttonSpinner = loginBtn.querySelector('.button-spinner');

            // 清除之前的错误
            clearErrors();

            // 验证表单
            let isValid = true;

            if (!validateEmail(email.value)) {
                showError('email', '请输入有效的企业邮箱地址');
                isValid = false;
            }

            if (!validatePassword(password.value)) {
                showError('password', '密码长度至少为8位，包含字母和数字');
                isValid = false;
            }

            if (!isValid) return;

            // 显示加载状态
            loginBtn.disabled = true;
            buttonText.style.display = 'none';
            buttonSpinner.style.display = 'flex';

            // 模拟登录过程
            setTimeout(() => {
                // 这里应该是实际的登录API调用
                console.log('登录信息:', {
                    email: email.value,
                    password: password.value,
                    remember: document.getElementById('remember').checked
                });

                // 模拟成功登录
                showSuccess('登录成功！正在跳转...');

                setTimeout(() => {
                    // 这里应该跳转到实际的应用页面
                    alert('登录成功！');
                    resetLoginButton();
                }, 1500);

            }, 2000);
        }

        // 邮箱验证
        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email) && email.length > 0;
        }

        // 密码验证
        function validatePassword(password) {
            return password.length >= 8 && /[A-Za-z]/.test(password) && /[0-9]/.test(password);
        }

        // 显示错误信息
        function showError(fieldId, message) {
            const field = document.getElementById(fieldId);
            const errorDiv = document.getElementById(fieldId + '-error');

            field.classList.add('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'flex';
        }

        // 清除错误信息
        function clearErrors() {
            const errorMessages = document.querySelectorAll('.error-message');
            const errorFields = document.querySelectorAll('.form-input.error');

            errorMessages.forEach(error => {
                error.style.display = 'none';
                error.textContent = '';
            });

            errorFields.forEach(field => {
                field.classList.remove('error');
            });
        }

        // 显示成功信息
        function showSuccess(message) {
            // 可以添加成功提示的UI
            console.log('Success:', message);
        }

        // 重置登录按钮
        function resetLoginButton() {
            const loginBtn = document.getElementById('loginBtn');
            const buttonText = loginBtn.querySelector('.button-text');
            const buttonSpinner = loginBtn.querySelector('.button-spinner');

            loginBtn.disabled = false;
            buttonText.style.display = 'block';
            buttonSpinner.style.display = 'none';
        }

        // 密码显示切换
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eye-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.innerHTML = `
                    <path d="M11.83,9L15,12.16C15,12.11 15,12.05 15,12A3,3 0 0,0 12,9C11.94,9 11.89,9 11.83,9M7.53,9.8L9.08,11.35C9.03,11.56 9,11.77 9,12A3,3 0 0,0 12,15C12.22,15 12.44,14.97 12.65,14.92L14.2,16.47C13.53,16.8 12.79,17 12,17A5,5 0 0,1 7,12C7,11.21 7.2,10.47 7.53,9.8M2,4.27L4.28,6.55L4.73,7C3.08,8.3 1.78,10 1,12C2.73,16.39 7,19.5 12,19.5C13.55,19.5 15.03,19.2 16.38,18.66L16.81,19.09L19.73,22L21,20.73L3.27,3M12,7A5,5 0 0,1 17,12C17,12.64 16.87,13.26 16.64,13.82L19.57,16.75C21.07,15.5 22.27,13.86 23,12C21.27,7.61 17,4.5 12,4.5C10.6,4.5 9.26,4.75 8,5.2L10.17,7.35C10.76,7.13 11.37,7 12,7Z"/>
                `;
            } else {
                passwordInput.type = 'password';
                eyeIcon.innerHTML = `
                    <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                `;
            }
        }

        // SSO登录处理
        function handleSSOLogin(provider) {
            console.log(`使用 ${provider} 进行 SSO 登录`);

            // 这里应该重定向到相应的SSO提供商
            switch(provider) {
                case 'microsoft':
                    // window.location.href = 'https://login.microsoftonline.com/...';
                    alert('正在跳转到 Microsoft 365 登录...');
                    break;
                case 'google':
                    // window.location.href = 'https://accounts.google.com/oauth/authorize?...';
                    alert('正在跳转到 Google Workspace 登录...');
                    break;
                default:
                    alert('不支持的登录方式');
            }
        }

        // 忘记密码处理
        function handleForgotPassword(event) {
            event.preventDefault();

            const email = document.getElementById('email').value;
            if (email) {
                alert(`密码重置链接将发送到: ${email}`);
            } else {
                alert('请先输入您的邮箱地址');
                document.getElementById('email').focus();
            }
        }

        // 页脚链接处理
        function handleSupport(event) {
            event.preventDefault();
            alert('技术支持: <EMAIL>\n电话: ************');
        }

        function handlePrivacy(event) {
            event.preventDefault();
            // window.open('/privacy-policy', '_blank');
            alert('隐私政策页面');
        }

        function handleTerms(event) {
            event.preventDefault();
            // window.open('/terms-of-service', '_blank');
            alert('服务条款页面');
        }

        // 实时表单验证
        document.addEventListener('DOMContentLoaded', function() {
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');

            emailInput.addEventListener('blur', function() {
                if (this.value && !validateEmail(this.value)) {
                    showError('email', '请输入有效的企业邮箱地址');
                } else {
                    clearFieldError('email');
                }
            });

            passwordInput.addEventListener('blur', function() {
                if (this.value && !validatePassword(this.value)) {
                    showError('password', '密码长度至少为8位，包含字母和数字');
                } else {
                    clearFieldError('password');
                }
            });

            // 输入时清除错误
            emailInput.addEventListener('input', function() {
                if (this.classList.contains('error')) {
                    clearFieldError('email');
                }
            });

            passwordInput.addEventListener('input', function() {
                if (this.classList.contains('error')) {
                    clearFieldError('password');
                }
            });
        });

        // 清除单个字段错误
        function clearFieldError(fieldId) {
            const field = document.getElementById(fieldId);
            const errorDiv = document.getElementById(fieldId + '-error');

            field.classList.remove('error');
            errorDiv.style.display = 'none';
            errorDiv.textContent = '';
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', function(event) {
            // Enter键提交表单
            if (event.key === 'Enter' && !event.shiftKey) {
                const activeElement = document.activeElement;
                if (activeElement.tagName === 'INPUT') {
                    const form = activeElement.closest('form');
                    if (form) {
                        event.preventDefault();
                        form.dispatchEvent(new Event('submit'));
                    }
                }
            }
        });
    </script>
</body>
</html>
